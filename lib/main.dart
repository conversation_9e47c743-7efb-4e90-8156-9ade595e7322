import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/feature/onboard/sign_up.dart';
import 'package:movie_map/feature/profile/viewmodel/profile_view_model.dart';
import 'package:movie_map/product/init/init_app.dart';
import 'package:movie_map/product/init/product_localization.dart';
import 'package:movie_map/product/service/battle-service/viewmodel/battle_view_model.dart';
import 'package:movie_map/product/service/movie%20service/movie_repository.dart';
import 'package:movie_map/product/state/provider/battle_provider.dart';
import 'package:movie_map/product/state/provider/create_user_provider.dart';
import 'package:movie_map/product/state/provider/marker_manager.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/state/provider/navigation_provider.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:movie_map/product/utility/constants/constants.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tmdb_api/tmdb_api.dart';

void main() async {
  await initApp();

  SharedPreferences prefs = await SharedPreferences.getInstance();
  int enterCount = prefs.getInt('enterCount') ?? 0;

  prefs.setInt('enterCount', enterCount + 1);

  bool isAuthenticated = FirebaseAuth.instance.currentUser != null;
  debugPrint('isAuthenticated: $isAuthenticated');

  runApp(
    ProductLocalization(
      child: MyApp(
        enterCount: enterCount,
        isAuthenticated: isAuthenticated,
      ),
    ),
  );
}

class MyApp extends StatelessWidget {
  final int enterCount;
  final bool isAuthenticated;

  const MyApp({
    super.key,
    required this.enterCount,
    required this.isAuthenticated,
  });

  @override
  Widget build(BuildContext context) {
    final movieRepository = MovieRepository(
      TMDB(
        ApiKeys(ApiConstants.instance.tmdbApiKey, ApiConstants.instance.tmdbApiReadToken),
        logConfig: const ConfigLogger(),
      ),
      FirebaseFirestore.instance,
    );
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => NavigationHelper()),
        ChangeNotifierProvider(create: (_) => MarkerManager()),
        ChangeNotifierProvider(create: (_) => UserManagerProvider()),
        ChangeNotifierProvider(create: (_) => MovieCollectionViewModel(movieRepository)),
        ChangeNotifierProvider(create: (_) => BattleProvider()),
        ChangeNotifierProvider(create: (_) => CreateUserProvider()),
        ChangeNotifierProvider(create: (_) => BattleViewModel()),
      ],
      child: MaterialApp(
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        theme: AppTheme().appTheme,
        home: enterCount == 0 || !isAuthenticated ? const SignUpView() : const BottomNavBarPages(),
      ),
    );
  }
}
