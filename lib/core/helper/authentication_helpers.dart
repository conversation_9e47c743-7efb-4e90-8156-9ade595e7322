import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:movie_map/product/model/user_model.dart';

class AuthenticationHelpers {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  Future<UserCredential?> signWithGoogle(BuildContext context) async {
    try {
      final GoogleSignIn googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'https://www.googleapis.com/auth/userinfo.profile',
        ],
      );

      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      debugPrint("Google Auth: $googleAuth");

      if (googleAuth.idToken == null) {
        debugPrint("Google Sign-In failed: idToken is null.");
        return null;
      }

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      debugPrint("Credential: $credential");

      UserCredential userCredential = await _firebaseAuth.signInWithCredential(credential);
      debugPrint("User Credential 1: $userCredential");
      return userCredential;
    } catch (e) {
      debugPrint("Google sign-in error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Google ile giriş başarısız: $e")),
      );
      return null;
    }
  }

  static Future<void> signOut() async {
    await FirebaseAuth.instance.signOut();
    await GoogleSignIn().signOut();
  }

  // add data to console firestore
  Future<void> saveUserData(String userId, UserModel userData) async {
    final FirebaseFirestore firestore = FirebaseFirestore.instance;
    try {
      await firestore.collection('users').doc(userId).set(userData.toMap());
      debugPrint('Data saved successfully for user $userId');
    } catch (e) {
      debugPrint('Failed to save data: $e');
      rethrow;
    }
  }

  // temporary func.
  String generateName(int length) {
    const String chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    Random random = Random();
    return String.fromCharCodes(Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  Future<String?> checkUsername(String username) async {
    // Validation checks
    if (username.contains(RegExp(r'[A-Z]'))) {
      return 'Username should not contain uppercase letters.';
    }
    if (username.contains(' ')) {
      return 'Username should not contain spaces.';
    }
    if (username.length > 16) {
      return 'Username should not exceed 16 characters.';
    }
    if (username.isEmpty) {
      return '';
    }

    final FirebaseFirestore firestore = FirebaseFirestore.instance;
    try {
      // Check if username exists in the Firestore database
      final snapshot = await firestore.collection('users').where('username', isEqualTo: username).get();

      if (snapshot.docs.isNotEmpty) {
        return 'Username already exists.';
      }
      return null;
    } catch (e) {
      debugPrint('Failed to check username: $e');
      rethrow;
    }
  }

  // delete user account
  // static Future<void> deleteAccount() async {
  //   final user = FirebaseAuth.instance.currentUser;
  //   await user!.delete();
  //   // move user data to bin collection
  //   //* TODO: await FirebaseFirestore.instance.collection('bin').doc(user.uid).set();
  // }
}
